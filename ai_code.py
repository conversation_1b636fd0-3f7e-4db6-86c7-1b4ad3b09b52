import matplotlib.pyplot as plt
import numpy as np

def plot_adaptive_vspans(ax, num_spans, total_alpha=0.8):
    """
    Plots a number of vspans with an alpha that adapts to the quantity.

    Args:
        ax (matplotlib.axes.Axes): The axes object to plot on.
        num_spans (int): The number of vspans to plot.
        total_alpha (float): The target maximum alpha for overlapping regions.
    """
    if num_spans <= 0:
        return # Do nothing if there are no spans to plot

    # Calculate the individual alpha required to reach the total_alpha
    individual_alpha = 1 - (1 - total_alpha)**(1 / num_spans)

    # Generate some random data for the spans for demonstration
    np.random.seed(42)
    starts = np.random.rand(num_spans) * 10
    widths = np.random.rand(num_spans) * 2

    # Plot each vspan
    for i in range(num_spans):
        ax.axvspan(
            starts[i],
            starts[i] + widths[i],
            alpha=individual_alpha,
            color='cornflowerblue'
        )
    
    # Add some background data for context
    x_data = np.linspace(0, 12, 200)
    y_data = np.sin(x_data) + np.random.randn(200) * 0.1
    ax.plot(x_data, y_data, color='black', linewidth=0.7)
    
    ax.set_title(f'{num_spans} Spans\nIndividual alpha: {individual_alpha:.3f}')
    ax.set_ylim(-2, 2)
    ax.set_xlim(0, 12)


# --- Create plots to demonstrate the effect ---
fig, axes = plt.subplots(1, 3, figsize=(18, 5), sharey=True)

# Example with a few spans
plot_adaptive_vspans(axes[0], num_spans=5)

# Example with a medium number of spans
plot_adaptive_vspans(axes[1], num_spans=20)

# Example with many spans
plot_adaptive_vspans(axes[2], num_spans=100)

fig.suptitle('Adaptive Alpha for Overlapping vspans', fontsize=16)
plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.show()