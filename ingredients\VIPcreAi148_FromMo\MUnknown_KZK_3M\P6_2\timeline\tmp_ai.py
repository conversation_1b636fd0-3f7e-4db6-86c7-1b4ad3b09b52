import os
import pandas as pd

# ------------------------------------------------------------------
# 1)  Paths & set‑up
# ------------------------------------------------------------------
xlsx_file = "Arduino time point P6_2.xlsx"          # source workbook

# “old label  →  new label” mapping
EVENT_MAP = {
    "Blank": "Blank",
    "Puff": "VerticalPuff"
}


xls = pd.ExcelFile(xlsx_file)
for sheet_id, sheet in enumerate(xls.sheet_names):
    df = xls.parse(sheet, header=None)  # type: pd.DataFrame

    event_list, time_list = df.get(0), df.get(2)
    new_event_list, new_time_list = ["task start",], [0,]
    for tmp_event, tmp_time in zip(event_list, time_list):
        transform_event = EVENT_MAP[tmp_event]
        new_event_list.append(transform_event+"On")
        new_event_list.append(transform_event+"Off")
        new_time_list.append(float(tmp_time))
        new_time_list.append(float(tmp_time)+500)

    new_df = pd.DataFrame({
        "time": new_time_list,
        "details": new_event_list
    })

    out_path = f"TIMELINE_{int(sheet_id/2)+1:02d}_KZK_3M_SAT_{(sheet_id%2)+3}_SAT.csv"
    new_df.to_csv(out_path, index=False)        # no row indices
    print(f"✓ Saved {out_path}")

